"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/dashboard",{

/***/ "./pages/admin/dashboard.js":
/*!**********************************!*\
  !*** ./pages/admin/dashboard.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [codes, setCodes] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [showCreateForm, setShowCreateForm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [newCodeForm, setNewCodeForm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        description: \"\",\n        expiresInDays: 30\n    });\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // 检查认证状态\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        checkAuth();\n    }, []);\n    // 加载激活码数据\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (isAuthenticated) {\n            loadActivationCodes();\n        }\n    }, [\n        isAuthenticated\n    ]);\n    const checkAuth = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/auth\");\n            const result = await response.json();\n            if (result.authenticated) {\n                setIsAuthenticated(true);\n            } else {\n                router.push(\"/admin/login\");\n            }\n        } catch (error) {\n            router.push(\"/admin/login\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loadActivationCodes = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/activation-codes\");\n            if (response.ok) {\n                const data = await response.json();\n                setCodes(data.codes);\n                setStats(data.stats);\n            }\n        } catch (error) {\n            console.error(\"Failed to load activation codes:\", error);\n        }\n    };\n    const handleCreateCode = async (e)=>{\n        e.preventDefault();\n        setIsCreating(true);\n        try {\n            const response = await fetch(\"/api/admin/activation-codes\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newCodeForm)\n            });\n            if (response.ok) {\n                setNewCodeForm({\n                    description: \"\",\n                    expiresInDays: 30\n                });\n                setShowCreateForm(false);\n                loadActivationCodes() // 重新加载数据\n                ;\n            } else {\n                alert(\"创建激活码失败\");\n            }\n        } catch (error) {\n            alert(\"创建激活码失败\");\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const handleToggleCode = async (codeId, action)=>{\n        try {\n            const response = await fetch(\"/api/admin/activation-codes\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    codeId,\n                    action\n                })\n            });\n            if (response.ok) {\n                loadActivationCodes() // 重新加载数据\n                ;\n            } else {\n                alert(\"操作失败\");\n            }\n        } catch (error) {\n            alert(\"操作失败\");\n        }\n    };\n    const handleDeleteCode = async (codeId)=>{\n        console.log(\"\\uD83D\\uDDD1️ [FRONTEND DEBUG] Delete request initiated for codeId:\", codeId);\n        if (!confirm(\"确定要删除这个激活码吗？\")) {\n            return;\n        }\n        try {\n            console.log(\"\\uD83D\\uDDD1️ [FRONTEND DEBUG] Sending DELETE request with URL parameter...\");\n            const response = await fetch(\"/api/admin/activation-codes?codeId=\".concat(encodeURIComponent(codeId)), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log(\"\\uD83D\\uDDD1️ [FRONTEND DEBUG] Response status:\", response.status);\n            console.log(\"\\uD83D\\uDDD1️ [FRONTEND DEBUG] Response ok:\", response.ok);\n            if (response.ok) {\n                console.log(\"✅ [FRONTEND DEBUG] Delete successful, reloading codes...\");\n                loadActivationCodes() // 重新加载数据\n                ;\n            } else {\n                const errorData = await response.json().catch(()=>({}));\n                console.log(\"❌ [FRONTEND DEBUG] Delete failed:\", errorData);\n                alert(\"删除失败: \".concat(errorData.error || \"未知错误\"));\n            }\n        } catch (error) {\n            console.log(\"\\uD83D\\uDCA5 [FRONTEND DEBUG] Delete error:\", error);\n            alert(\"删除失败: \" + error.message);\n        }\n    };\n    const handleLogout = async ()=>{\n        try {\n            await fetch(\"/api/admin/auth\", {\n                method: \"DELETE\"\n            });\n            router.push(\"/admin/login\");\n        } catch (error) {\n            router.push(\"/admin/login\");\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleString(\"zh-CN\");\n    };\n    const getStatusBadge = (code)=>{\n        if (code.isUsed) return {\n            text: \"已使用\",\n            class: \"used\"\n        };\n        if (!code.isActive) return {\n            text: \"已禁用\",\n            class: \"disabled\"\n        };\n        if (new Date() > new Date(code.expiresAt)) return {\n            text: \"已过期\",\n            class: \"expired\"\n        };\n        return {\n            text: \"可用\",\n            class: \"available\"\n        };\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"loading-container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null // 会被重定向到登录页\n        ;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: \"jsx-d10189405005aa32\",\n                    children: \"激活码管理 - Great Heights School\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-d10189405005aa32\" + \" \" + \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-d10189405005aa32\" + \" \" + \"main-card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                className: \"jsx-d10189405005aa32\" + \" \" + \"header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d10189405005aa32\" + \" \" + \"header-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d10189405005aa32\" + \" \" + \"header-left\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d10189405005aa32\" + \" \" + \"logo-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"logo-icon\",\n                                                        children: \"\\uD83C\\uDF93\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"title-section\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"jsx-d10189405005aa32\",\n                                                                children: \"激活码管理系统\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"subtitle\",\n                                                                children: \"Great Heights School\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d10189405005aa32\" + \" \" + \"header-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d10189405005aa32\" + \" \" + \"header-actions\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowCreateForm(true),\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"btn btn-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"16\",\n                                                                height: \"16\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"none\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"jsx-d10189405005aa32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M12 5v14m-7-7h14\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    className: \"jsx-d10189405005aa32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"生成激活码\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLogout,\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"btn btn-secondary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"16\",\n                                                                height: \"16\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"none\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"jsx-d10189405005aa32\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5z\",\n                                                                        fill: \"currentColor\",\n                                                                        className: \"jsx-d10189405005aa32\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 208,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z\",\n                                                                        fill: \"currentColor\",\n                                                                        className: \"jsx-d10189405005aa32\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"登出\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"jsx-d10189405005aa32\" + \" \" + \"main-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stats-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"jsx-d10189405005aa32\",\n                                                children: \"数据概览\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 221,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stats-grid\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-card\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-header\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-icon total\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"20\",\n                                                                            height: \"20\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"jsx-d10189405005aa32\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M9 11H7v8h2v-8zm4-4h-2v12h2V7zm4-2h-2v14h2V5z\",\n                                                                                fill: \"currentColor\",\n                                                                                className: \"jsx-d10189405005aa32\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 227,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 226,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"jsx-d10189405005aa32\",\n                                                                        children: \"总计\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-number\",\n                                                                children: stats.total || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-desc\",\n                                                                children: \"激活码总数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-card\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-header\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-icon available\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"20\",\n                                                                            height: \"20\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"jsx-d10189405005aa32\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z\",\n                                                                                fill: \"currentColor\",\n                                                                                className: \"jsx-d10189405005aa32\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 239,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 238,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"jsx-d10189405005aa32\",\n                                                                        children: \"可用\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-number available\",\n                                                                children: stats.available || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-desc\",\n                                                                children: \"可正常使用\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-card\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-header\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-icon used\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"20\",\n                                                                            height: \"20\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"jsx-d10189405005aa32\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\",\n                                                                                fill: \"currentColor\",\n                                                                                className: \"jsx-d10189405005aa32\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 251,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"jsx-d10189405005aa32\",\n                                                                        children: \"已使用\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-number used\",\n                                                                children: stats.used || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-desc\",\n                                                                children: \"已被激活\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-card\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-header\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-icon expired\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"20\",\n                                                                            height: \"20\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"jsx-d10189405005aa32\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-6V7h2v4h4v2z\",\n                                                                                fill: \"currentColor\",\n                                                                                className: \"jsx-d10189405005aa32\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 262,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"jsx-d10189405005aa32\",\n                                                                        children: \"已过期\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-number expired\",\n                                                                children: stats.expired || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-desc\",\n                                                                children: \"超过有效期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 222,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 220,\n                                        columnNumber: 11\n                                    }, this),\n                                    showCreateForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: ()=>setShowCreateForm(false),\n                                        className: \"jsx-d10189405005aa32\" + \" \" + \"modal-overlay\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: (e)=>e.stopPropagation(),\n                                            className: \"jsx-d10189405005aa32\" + \" \" + \"modal\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"jsx-d10189405005aa32\",\n                                                    children: \"生成新激活码\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleCreateCode,\n                                                    className: \"jsx-d10189405005aa32\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"form-group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: \"描述\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: newCodeForm.description,\n                                                                    onChange: (e)=>setNewCodeForm({\n                                                                            ...newCodeForm,\n                                                                            description: e.target.value\n                                                                        }),\n                                                                    placeholder: \"激活码用途描述（可选）\",\n                                                                    className: \"jsx-d10189405005aa32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"form-group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: \"有效期（天）\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: newCodeForm.expiresInDays,\n                                                                    onChange: (e)=>setNewCodeForm({\n                                                                            ...newCodeForm,\n                                                                            expiresInDays: parseInt(e.target.value)\n                                                                        }),\n                                                                    min: \"1\",\n                                                                    max: \"365\",\n                                                                    required: true,\n                                                                    className: \"jsx-d10189405005aa32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"form-actions\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setShowCreateForm(false),\n                                                                    className: \"jsx-d10189405005aa32\" + \" \" + \"btn btn-secondary\",\n                                                                    children: \"取消\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"submit\",\n                                                                    disabled: isCreating,\n                                                                    className: \"jsx-d10189405005aa32\" + \" \" + \"btn btn-primary\",\n                                                                    children: isCreating ? \"生成中...\" : \"生成\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 276,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 275,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-d10189405005aa32\" + \" \" + \"codes-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d10189405005aa32\" + \" \" + \"section-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"jsx-d10189405005aa32\",\n                                                        children: \"激活码列表\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"section-actions\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"search-box\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    width: \"16\",\n                                                                    height: \"16\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    fill: \"none\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"m21 21-6-6m2-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0z\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"2\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        className: \"jsx-d10189405005aa32\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"搜索激活码...\",\n                                                                    className: \"jsx-d10189405005aa32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 321,\n                                                columnNumber: 13\n                                            }, this),\n                                            codes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d10189405005aa32\" + \" \" + \"empty-state\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"empty-icon\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"48\",\n                                                            height: \"48\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"jsx-d10189405005aa32\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M9 11H7v8h2v-8zm4-4h-2v12h2V7zm4-2h-2v14h2V5z\",\n                                                                fill: \"currentColor\",\n                                                                className: \"jsx-d10189405005aa32\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-d10189405005aa32\",\n                                                        children: \"暂无激活码\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-d10189405005aa32\",\n                                                        children: '点击\"生成激活码\"按钮创建第一个激活码'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 333,\n                                                columnNumber: 15\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d10189405005aa32\" + \" \" + \"codes-table\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"table-header\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"col-code\",\n                                                                children: \"激活码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"col-status\",\n                                                                children: \"状态\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"col-created\",\n                                                                children: \"创建时间\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"col-expires\",\n                                                                children: \"过期时间\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"col-user\",\n                                                                children: \"使用者\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"col-actions\",\n                                                                children: \"操作\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    codes.map((code)=>{\n                                                        const status = getStatusBadge(code);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"table-row\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d10189405005aa32\" + \" \" + \"code-cell\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"code-text\",\n                                                                            children: code.code\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        code.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"code-desc\",\n                                                                            children: code.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 359,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"status-badge \".concat(status.class),\n                                                                        children: status.text\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: formatDate(code.createdAt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: formatDate(code.expiresAt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: code.usedBy || \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d10189405005aa32\" + \" \" + \"actions\",\n                                                                    children: [\n                                                                        !code.isUsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleToggleCode(code.id, code.isActive ? \"disable\" : \"enable\"),\n                                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"btn btn-sm \".concat(code.isActive ? \"btn-warning\" : \"btn-success\"),\n                                                                            children: code.isActive ? \"禁用\" : \"启用\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleDeleteCode(code.id),\n                                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"btn btn-sm btn-danger\",\n                                                                            children: \"删除\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, code.id, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 19\n                                                        }, this);\n                                                    })\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 320,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-d10189405005aa32\" + \" \" + \"footer\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-d10189405005aa32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"jsx-d10189405005aa32\" + \" \" + \"powered-by\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"14\",\n                                        height: \"14\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"currentColor\",\n                                        className: \"jsx-d10189405005aa32\" + \" \" + \"icon\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\",\n                                            className: \"jsx-d10189405005aa32\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 400,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Powered by\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://www.ghs.red/\",\n                                        target: \"_blank\",\n                                        rel: \"noopener\",\n                                        className: \"jsx-d10189405005aa32\",\n                                        children: \"Garbage Human Studio\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"d10189405005aa32\",\n                children: '.loading-container.jsx-d10189405005aa32{min-height:100vh;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;background:#fafafa;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Roboto\",\"Oxygen\",\"Ubuntu\",\"Cantarell\",\"Fira Sans\",\"Droid Sans\",\"Helvetica Neue\",sans-serif}.spinner.jsx-d10189405005aa32{width:32px;height:32px;border:3px solid#e1e5e9;border-top:3px solid#111827;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;-webkit-animation:spin.8s linear infinite;-moz-animation:spin.8s linear infinite;-o-animation:spin.8s linear infinite;animation:spin.8s linear infinite;margin-bottom:16px}@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}.container.jsx-d10189405005aa32{min-height:100vh;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;background:#fafafa;padding:20px;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Roboto\",\"Oxygen\",\"Ubuntu\",\"Cantarell\",\"Fira Sans\",\"Droid Sans\",\"Helvetica Neue\",sans-serif}.main-card.jsx-d10189405005aa32{background:#fff;max-width:1200px;width:100%;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;border:1px solid#e1e5e9;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1);overflow:hidden}.header.jsx-d10189405005aa32{background:#fff;border-bottom:1px solid#e1e5e9;padding:20px 32px}.header-content.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.header-left.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.header-right.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.logo-section.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:16px}.logo-icon.jsx-d10189405005aa32{font-size:32px;line-height:1}.title-section.jsx-d10189405005aa32 h1.jsx-d10189405005aa32{color:#111827;margin:0;font-size:24px;font-weight:600;letter-spacing:-.02em;line-height:1.2}.subtitle.jsx-d10189405005aa32{color:#6b7280;margin:2px 0 0 0;font-size:15px;font-weight:400;line-height:1.2}.header-actions.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px}.main-content.jsx-d10189405005aa32{padding:32px}.stats-section.jsx-d10189405005aa32{margin-bottom:48px}.stats-section.jsx-d10189405005aa32 h2.jsx-d10189405005aa32{color:#111827;font-size:20px;font-weight:600;margin-bottom:24px;letter-spacing:-.02em}.stats-grid.jsx-d10189405005aa32{display:grid;grid-template-columns:repeat(auto-fit,minmax(240px,1fr));gap:20px}.stat-card.jsx-d10189405005aa32{background:#fff;padding:24px;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;border:1px solid#e1e5e9;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1);-webkit-transition:all.2s ease-out;-moz-transition:all.2s ease-out;-o-transition:all.2s ease-out;transition:all.2s ease-out}.stat-card.jsx-d10189405005aa32:hover{border-color:#d1d5db;-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 4px 12px rgba(0,0,0,.08);-moz-box-shadow:0 4px 12px rgba(0,0,0,.08);box-shadow:0 4px 12px rgba(0,0,0,.08)}.stat-header.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:12px;margin-bottom:16px}.stat-icon.jsx-d10189405005aa32{width:40px;height:40px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.stat-icon.total.jsx-d10189405005aa32{background:#f3f4f6;color:#374151}.stat-icon.available.jsx-d10189405005aa32{background:#ecfdf5;color:#16a34a}.stat-icon.used.jsx-d10189405005aa32{background:#eff6ff;color:#2563eb}.stat-icon.expired.jsx-d10189405005aa32{background:#fef2f2;color:#dc2626}.stat-card.jsx-d10189405005aa32 h3.jsx-d10189405005aa32{margin:0;color:#374151;font-size:14px;font-weight:500}.stat-number.jsx-d10189405005aa32{font-size:28px;font-weight:700;color:#111827;margin-bottom:4px}.stat-desc.jsx-d10189405005aa32{color:#6b7280;font-size:13px;margin:0}.stat-number.available.jsx-d10189405005aa32{color:#16a34a}.stat-number.used.jsx-d10189405005aa32{color:#2563eb}.stat-number.expired.jsx-d10189405005aa32{color:#dc2626}.codes-section.jsx-d10189405005aa32{background:#fff;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;border:1px solid#e1e5e9;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1)}.section-header.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;padding:24px 24px 0 24px;margin-bottom:24px}.section-header.jsx-d10189405005aa32 h2.jsx-d10189405005aa32{margin:0;color:#111827;font-size:18px;font-weight:600;letter-spacing:-.02em}.section-actions.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px}.search-box.jsx-d10189405005aa32{position:relative;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.search-box.jsx-d10189405005aa32 svg.jsx-d10189405005aa32{position:absolute;left:12px;color:#9ca3af;z-index:1}.search-box.jsx-d10189405005aa32 input.jsx-d10189405005aa32{padding:8px 12px 8px 36px;border:1px solid#d1d5db;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;font-size:14px;width:200px;-webkit-transition:all.15s ease-out;-moz-transition:all.15s ease-out;-o-transition:all.15s ease-out;transition:all.15s ease-out}.search-box.jsx-d10189405005aa32 input.jsx-d10189405005aa32:focus{outline:none;border-color:#111827;-webkit-box-shadow:0 0 0 3px rgba(17,24,39,.1);-moz-box-shadow:0 0 0 3px rgba(17,24,39,.1);box-shadow:0 0 0 3px rgba(17,24,39,.1)}.empty-state.jsx-d10189405005aa32{text-align:center;padding:64px 24px}.empty-icon.jsx-d10189405005aa32{margin:0 auto 16px;width:48px;height:48px;color:#d1d5db}.empty-state.jsx-d10189405005aa32 h3.jsx-d10189405005aa32{color:#111827;font-size:16px;font-weight:600;margin:0 0 8px 0}.empty-state.jsx-d10189405005aa32 p.jsx-d10189405005aa32{color:#6b7280;font-size:14px;margin:0}.codes-table.jsx-d10189405005aa32{overflow-x:auto}.table-header.jsx-d10189405005aa32,.table-row.jsx-d10189405005aa32{display:grid;grid-template-columns:2fr 1fr 1.5fr 1.5fr 1.5fr 1fr;gap:16px;padding:16px 24px;border-bottom:1px solid#f3f4f6;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.table-header.jsx-d10189405005aa32{font-weight:600;color:#374151;background:#f9fafb;font-size:13px;text-transform:uppercase;letter-spacing:.05em;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}.code-cell.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:4px}.code-text.jsx-d10189405005aa32{font-family:monospace;font-weight:600;color:#333}.code-desc.jsx-d10189405005aa32{font-size:12px;color:#666}.status-badge.jsx-d10189405005aa32{padding:4px 8px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;font-size:12px;font-weight:500}.status-badge.available.jsx-d10189405005aa32{background:#d4edda;color:#155724}.status-badge.used.jsx-d10189405005aa32{background:#e2e3e5;color:#383d41}.status-badge.disabled.jsx-d10189405005aa32{background:#f8d7da;color:#721c24}.status-badge.expired.jsx-d10189405005aa32{background:#fff3cd;color:#856404}.actions.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:8px}.btn.jsx-d10189405005aa32{display:-webkit-inline-box;display:-webkit-inline-flex;display:-moz-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:6px;padding:8px 16px;border:1px solid transparent;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;cursor:pointer;font-size:14px;font-weight:500;-webkit-transition:all.15s ease-out;-moz-transition:all.15s ease-out;-o-transition:all.15s ease-out;transition:all.15s ease-out;text-decoration:none}.btn-primary.jsx-d10189405005aa32{background:#111827;color:#fff;border-color:#111827}.btn-primary.jsx-d10189405005aa32:hover{background:#374151;border-color:#374151;-webkit-transform:translatey(-1px);-moz-transform:translatey(-1px);-ms-transform:translatey(-1px);-o-transform:translatey(-1px);transform:translatey(-1px);-webkit-box-shadow:0 2px 4px rgba(0,0,0,.1);-moz-box-shadow:0 2px 4px rgba(0,0,0,.1);box-shadow:0 2px 4px rgba(0,0,0,.1)}.btn-secondary.jsx-d10189405005aa32{background:#fff;color:#374151;border-color:#d1d5db}.btn-secondary.jsx-d10189405005aa32:hover{background:#f9fafb;border-color:#9ca3af;-webkit-transform:translatey(-1px);-moz-transform:translatey(-1px);-ms-transform:translatey(-1px);-o-transform:translatey(-1px);transform:translatey(-1px);-webkit-box-shadow:0 2px 4px rgba(0,0,0,.05);-moz-box-shadow:0 2px 4px rgba(0,0,0,.05);box-shadow:0 2px 4px rgba(0,0,0,.05)}.btn-success.jsx-d10189405005aa32{background:#16a34a;color:#fff;border-color:#16a34a}.btn-success.jsx-d10189405005aa32:hover{background:#15803d;border-color:#15803d}.btn-warning.jsx-d10189405005aa32{background:#f59e0b;color:#fff;border-color:#f59e0b}.btn-warning.jsx-d10189405005aa32:hover{background:#d97706;border-color:#d97706}.btn-danger.jsx-d10189405005aa32{background:#dc2626;color:#fff;border-color:#dc2626}.btn-danger.jsx-d10189405005aa32:hover{background:#b91c1c;border-color:#b91c1c}.btn-sm.jsx-d10189405005aa32{padding:4px 8px;font-size:12px}.modal-overlay.jsx-d10189405005aa32{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.5);display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;z-index:1000}.modal.jsx-d10189405005aa32{background:#fff;padding:24px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;width:90%;max-width:400px}.modal.jsx-d10189405005aa32 h2.jsx-d10189405005aa32{margin:0 0 20px 0;color:#333}.form-group.jsx-d10189405005aa32{margin-bottom:16px}.form-group.jsx-d10189405005aa32 label.jsx-d10189405005aa32{display:block;margin-bottom:4px;color:#333;font-weight:500}.form-group.jsx-d10189405005aa32 input.jsx-d10189405005aa32{width:100%;padding:8px 12px;border:1px solid#ddd;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;font-size:14px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.form-group.jsx-d10189405005aa32 input.jsx-d10189405005aa32:focus{outline:none;border-color:#4285f4}.form-actions.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px;-webkit-box-pack:end;-webkit-justify-content:flex-end;-moz-box-pack:end;-ms-flex-pack:end;justify-content:flex-end}@media(max-width:768px){.header-content.jsx-d10189405005aa32{padding:12px 16px;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px;-webkit-box-align:stretch;-webkit-align-items:stretch;-moz-box-align:stretch;-ms-flex-align:stretch;align-items:stretch}.logo.jsx-d10189405005aa32{-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.header-actions.jsx-d10189405005aa32{-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.main-content.jsx-d10189405005aa32{padding:16px}.stats-grid.jsx-d10189405005aa32{grid-template-columns:1fr;gap:16px}.section-header.jsx-d10189405005aa32{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px;-webkit-box-align:stretch;-webkit-align-items:stretch;-moz-box-align:stretch;-ms-flex-align:stretch;align-items:stretch;padding:16px 16px 0 16px}.search-box.jsx-d10189405005aa32 input.jsx-d10189405005aa32{width:100%}.table-header.jsx-d10189405005aa32,.table-row.jsx-d10189405005aa32{grid-template-columns:1fr;gap:8px;padding:12px 16px}.table-header.jsx-d10189405005aa32>div.jsx-d10189405005aa32,.table-row.jsx-d10189405005aa32>div.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.table-header.jsx-d10189405005aa32>div.jsx-d10189405005aa32::before,.table-row.jsx-d10189405005aa32>div.jsx-d10189405005aa32::before{content:attr(data-label);font-weight:600;color:#6b7280;font-size:12px}.col-code.jsx-d10189405005aa32::before{content:\"激活码\"}.col-status.jsx-d10189405005aa32::before{content:\"状态\"}.col-created.jsx-d10189405005aa32::before{content:\"创建时间\"}.col-expires.jsx-d10189405005aa32::before{content:\"过期时间\"}.col-user.jsx-d10189405005aa32::before{content:\"使用者\"}.col-actions.jsx-d10189405005aa32::before{content:\"操作\"}}.footer.jsx-d10189405005aa32{margin-top:32px;color:#9ca3af;font-size:13px;text-align:center}.powered-by.jsx-d10189405005aa32{display:-webkit-inline-box;display:-webkit-inline-flex;display:-moz-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:4px}.icon.jsx-d10189405005aa32{-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.footer.jsx-d10189405005aa32 a.jsx-d10189405005aa32{color:#6b7280;text-decoration:none;font-weight:500;margin-left:4px}.footer.jsx-d10189405005aa32 a.jsx-d10189405005aa32:hover{color:#374151;text-decoration:underline}@media(max-width:1023px){.main-card.jsx-d10189405005aa32{max-width:100%;margin:0;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0;border-left:none;border-right:none}.container.jsx-d10189405005aa32{padding:0;-webkit-box-pack:start;-webkit-justify-content:flex-start;-moz-box-pack:start;-ms-flex-pack:start;justify-content:flex-start}}@media(max-width:768px){.header.jsx-d10189405005aa32{padding:16px 20px}.header-content.jsx-d10189405005aa32{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start}.header-actions.jsx-d10189405005aa32{width:100%;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}.main-content.jsx-d10189405005aa32{padding:20px}.logo-icon.jsx-d10189405005aa32{font-size:24px}.title-section.jsx-d10189405005aa32 h1.jsx-d10189405005aa32{font-size:20px}.subtitle.jsx-d10189405005aa32{font-size:13px}}@media(max-width:480px){.header.jsx-d10189405005aa32{padding:12px 16px}.main-content.jsx-d10189405005aa32{padding:16px}.stats-grid.jsx-d10189405005aa32{grid-template-columns:1fr;gap:16px}.stat-card.jsx-d10189405005aa32{padding:20px}.btn.jsx-d10189405005aa32{padding:8px 12px;font-size:13px}.header-actions.jsx-d10189405005aa32{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:8px}}'\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_s(AdminDashboard, \"7VLmiGYfVUOxxq6tsQ/GFOaJtSY=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/admin/dashboard.js\n"));

/***/ })

});