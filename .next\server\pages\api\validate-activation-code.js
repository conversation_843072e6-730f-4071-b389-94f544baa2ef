"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/validate-activation-code";
exports.ids = ["pages/api/validate-activation-code"];
exports.modules = {

/***/ "uuid":
/*!***********************!*\
  !*** external "uuid" ***!
  \***********************/
/***/ ((module) => {

module.exports = import("uuid");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./lib/activation-codes.js":
/*!*********************************!*\
  !*** ./lib/activation-codes.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"deleteActivationCode\": () => (/* binding */ deleteActivationCode),\n/* harmony export */   \"disableActivationCode\": () => (/* binding */ disableActivationCode),\n/* harmony export */   \"enableActivationCode\": () => (/* binding */ enableActivationCode),\n/* harmony export */   \"generateActivationCode\": () => (/* binding */ generateActivationCode),\n/* harmony export */   \"getActivationCodeStats\": () => (/* binding */ getActivationCodeStats),\n/* harmony export */   \"getAllActivationCodes\": () => (/* binding */ getAllActivationCodes),\n/* harmony export */   \"useActivationCode\": () => (/* binding */ useActivationCode),\n/* harmony export */   \"validateActivationCode\": () => (/* binding */ validateActivationCode)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"uuid\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([uuid__WEBPACK_IMPORTED_MODULE_2__]);\nuuid__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// lib/activation-codes.js\n\n\n\nconst CODES_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"data\", \"activation-codes.json\");\n// 确保数据目录存在\nfunction ensureDataDir() {\n    const dataDir = path__WEBPACK_IMPORTED_MODULE_1___default().dirname(CODES_FILE);\n    if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(dataDir)) {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(dataDir, {\n            recursive: true\n        });\n    }\n}\n// 读取激活码数据\nfunction readCodesData() {\n    ensureDataDir();\n    if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(CODES_FILE)) {\n        return [];\n    }\n    try {\n        const data = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(CODES_FILE, \"utf8\");\n        return JSON.parse(data);\n    } catch (error) {\n        console.error(\"Error reading activation codes:\", error);\n        return [];\n    }\n}\n// 写入激活码数据\nfunction writeCodesData(codes) {\n    ensureDataDir();\n    try {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(CODES_FILE, JSON.stringify(codes, null, 2));\n        return true;\n    } catch (error) {\n        console.error(\"Error writing activation codes:\", error);\n        return false;\n    }\n}\n// 生成激活码\nfunction generateActivationCode(description = \"\", expiresInDays = 30, maxUsageCount = 1) {\n    const codes = readCodesData();\n    const code = (0,uuid__WEBPACK_IMPORTED_MODULE_2__.v4)().replace(/-/g, \"\").substring(0, 16).toUpperCase();\n    const expiresAt = new Date();\n    expiresAt.setDate(expiresAt.getDate() + expiresInDays);\n    const newCode = {\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__.v4)(),\n        code,\n        description,\n        createdAt: new Date().toISOString(),\n        expiresAt: expiresAt.toISOString(),\n        maxUsageCount: Math.max(1, parseInt(maxUsageCount) || 1),\n        usedCount: 0,\n        isUsed: false,\n        usedAt: null,\n        usedBy: null,\n        usedByList: [],\n        isActive: true\n    };\n    codes.push(newCode);\n    if (writeCodesData(codes)) {\n        return newCode;\n    }\n    return null;\n}\n// 验证激活码\nfunction validateActivationCode(code) {\n    const codes = readCodesData();\n    const activationCode = codes.find((c)=>c.code === code.toUpperCase());\n    if (!activationCode) {\n        return {\n            valid: false,\n            error: \"INVALID_CODE\"\n        };\n    }\n    if (!activationCode.isActive) {\n        return {\n            valid: false,\n            error: \"DISABLED_CODE\"\n        };\n    }\n    // 检查使用次数限制\n    const usedCount = activationCode.usedCount || 0;\n    const maxUsageCount = activationCode.maxUsageCount || 1;\n    if (usedCount >= maxUsageCount) {\n        return {\n            valid: false,\n            error: \"USAGE_LIMIT_EXCEEDED\"\n        };\n    }\n    // 保持向后兼容性检查\n    if (activationCode.isUsed && maxUsageCount === 1) {\n        return {\n            valid: false,\n            error: \"USED_CODE\"\n        };\n    }\n    if (new Date() > new Date(activationCode.expiresAt)) {\n        return {\n            valid: false,\n            error: \"EXPIRED_CODE\"\n        };\n    }\n    return {\n        valid: true,\n        code: activationCode\n    };\n}\n// 使用激活码\nfunction useActivationCode(code, userEmail) {\n    const codes = readCodesData();\n    const codeIndex = codes.findIndex((c)=>c.code === code.toUpperCase());\n    if (codeIndex === -1) {\n        return false;\n    }\n    const validation = validateActivationCode(code);\n    if (!validation.valid) {\n        return false;\n    }\n    const activationCode = codes[codeIndex];\n    // 增加使用次数\n    activationCode.usedCount = (activationCode.usedCount || 0) + 1;\n    // 更新使用者列表\n    if (!activationCode.usedByList) {\n        activationCode.usedByList = [];\n    }\n    activationCode.usedByList.push({\n        email: userEmail,\n        usedAt: new Date().toISOString()\n    });\n    // 更新最后使用信息（保持向后兼容）\n    activationCode.usedAt = new Date().toISOString();\n    activationCode.usedBy = userEmail;\n    // 如果达到最大使用次数，标记为已使用（保持向后兼容）\n    const maxUsageCount = activationCode.maxUsageCount || 1;\n    if (activationCode.usedCount >= maxUsageCount) {\n        activationCode.isUsed = true;\n    }\n    return writeCodesData(codes);\n}\n// 获取所有激活码\nfunction getAllActivationCodes() {\n    return readCodesData();\n}\n// 禁用激活码\nfunction disableActivationCode(codeId) {\n    const codes = readCodesData();\n    const codeIndex = codes.findIndex((c)=>c.id === codeId);\n    if (codeIndex === -1) {\n        return false;\n    }\n    codes[codeIndex].isActive = false;\n    return writeCodesData(codes);\n}\n// 启用激活码\nfunction enableActivationCode(codeId) {\n    const codes = readCodesData();\n    const codeIndex = codes.findIndex((c)=>c.id === codeId);\n    if (codeIndex === -1) {\n        return false;\n    }\n    codes[codeIndex].isActive = true;\n    return writeCodesData(codes);\n}\n// 删除激活码\nfunction deleteActivationCode(codeId) {\n    const codes = readCodesData();\n    const filteredCodes = codes.filter((c)=>c.id !== codeId);\n    if (filteredCodes.length === codes.length) {\n        return false // 没有找到要删除的代码\n        ;\n    }\n    return writeCodesData(filteredCodes);\n}\n// 获取激活码统计信息\nfunction getActivationCodeStats() {\n    const codes = readCodesData();\n    const total = codes.length;\n    const active = codes.filter((c)=>c.isActive).length;\n    const expired = codes.filter((c)=>new Date() > new Date(c.expiresAt)).length;\n    // 重新计算使用状态，考虑使用次数\n    const used = codes.filter((c)=>{\n        const usedCount = c.usedCount || 0;\n        const maxUsageCount = c.maxUsageCount || 1;\n        return usedCount >= maxUsageCount;\n    }).length;\n    const available = codes.filter((c)=>{\n        const usedCount = c.usedCount || 0;\n        const maxUsageCount = c.maxUsageCount || 1;\n        return c.isActive && usedCount < maxUsageCount && new Date() <= new Date(c.expiresAt);\n    }).length;\n    // 新增统计：多次使用激活码相关\n    const multiUse = codes.filter((c)=>(c.maxUsageCount || 1) > 1).length;\n    const totalUsages = codes.reduce((sum, c)=>sum + (c.usedCount || 0), 0);\n    const partiallyUsed = codes.filter((c)=>{\n        const usedCount = c.usedCount || 0;\n        const maxUsageCount = c.maxUsageCount || 1;\n        return usedCount > 0 && usedCount < maxUsageCount;\n    }).length;\n    return {\n        total,\n        active,\n        used,\n        expired,\n        available,\n        multiUse,\n        totalUsages,\n        partiallyUsed\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/activation-codes.js\n");

/***/ }),

/***/ "(api)/./pages/api/validate-activation-code.js":
/*!***********************************************!*\
  !*** ./pages/api/validate-activation-code.js ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_activation_codes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/activation-codes */ \"(api)/./lib/activation-codes.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_activation_codes__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_activation_codes__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// pages/api/validate-activation-code.js\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        res.setHeader(\"Allow\", [\n            \"POST\"\n        ]);\n        return res.status(405).end(`Method ${req.method} Not Allowed`);\n    }\n    const { code  } = req.body;\n    if (!code) {\n        return res.status(400).json({\n            valid: false,\n            error: \"MISSING_CODE\",\n            message: \"请输入激活码\"\n        });\n    }\n    try {\n        const validation = (0,_lib_activation_codes__WEBPACK_IMPORTED_MODULE_0__.validateActivationCode)(code);\n        if (validation.valid) {\n            res.status(200).json({\n                valid: true,\n                message: \"激活码有效\"\n            });\n        } else {\n            let message = \"激活码无效\";\n            switch(validation.error){\n                case \"INVALID_CODE\":\n                    message = \"激活码不存在\";\n                    break;\n                case \"DISABLED_CODE\":\n                    message = \"激活码已被禁用\";\n                    break;\n                case \"USED_CODE\":\n                    message = \"激活码已被使用\";\n                    break;\n                case \"USAGE_LIMIT_EXCEEDED\":\n                    message = \"激活码使用次数已达上限\";\n                    break;\n                case \"EXPIRED_CODE\":\n                    message = \"激活码已过期\";\n                    break;\n            }\n            res.status(400).json({\n                valid: false,\n                error: validation.error,\n                message\n            });\n        }\n    } catch (error) {\n        console.error(\"Error validating activation code:\", error);\n        res.status(500).json({\n            valid: false,\n            error: \"SERVER_ERROR\",\n            message: \"服务器错误，请稍后重试\"\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/validate-activation-code.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(api)/./pages/api/validate-activation-code.js"));
module.exports = __webpack_exports__;

})();