"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/dashboard",{

/***/ "./pages/admin/dashboard.js":
/*!**********************************!*\
  !*** ./pages/admin/dashboard.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [codes, setCodes] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [showCreateForm, setShowCreateForm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [newCodeForm, setNewCodeForm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        description: \"\",\n        expiresInDays: 30,\n        maxUsageCount: 1\n    });\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // 检查认证状态\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        checkAuth();\n    }, []);\n    // 加载激活码数据\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (isAuthenticated) {\n            loadActivationCodes();\n        }\n    }, [\n        isAuthenticated\n    ]);\n    const checkAuth = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/auth\");\n            const result = await response.json();\n            if (result.authenticated) {\n                setIsAuthenticated(true);\n            } else {\n                router.push(\"/admin/login\");\n            }\n        } catch (error) {\n            router.push(\"/admin/login\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loadActivationCodes = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/activation-codes\");\n            if (response.ok) {\n                const data = await response.json();\n                setCodes(data.codes);\n                setStats(data.stats);\n            }\n        } catch (error) {\n            console.error(\"Failed to load activation codes:\", error);\n        }\n    };\n    const handleCreateCode = async (e)=>{\n        e.preventDefault();\n        setIsCreating(true);\n        try {\n            const response = await fetch(\"/api/admin/activation-codes\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newCodeForm)\n            });\n            if (response.ok) {\n                setNewCodeForm({\n                    description: \"\",\n                    expiresInDays: 30,\n                    maxUsageCount: 1\n                });\n                setShowCreateForm(false);\n                loadActivationCodes() // 重新加载数据\n                ;\n            } else {\n                alert(\"创建激活码失败\");\n            }\n        } catch (error) {\n            alert(\"创建激活码失败\");\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const handleToggleCode = async (codeId, action)=>{\n        try {\n            const response = await fetch(\"/api/admin/activation-codes\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    codeId,\n                    action\n                })\n            });\n            if (response.ok) {\n                loadActivationCodes() // 重新加载数据\n                ;\n            } else {\n                alert(\"操作失败\");\n            }\n        } catch (error) {\n            alert(\"操作失败\");\n        }\n    };\n    const handleDeleteCode = async (codeId)=>{\n        if (!confirm(\"确定要删除这个激活码吗？\")) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/admin/activation-codes?codeId=\".concat(encodeURIComponent(codeId)), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                loadActivationCodes() // 重新加载数据\n                ;\n            } else {\n                const errorData = await response.json().catch(()=>({}));\n                alert(\"删除失败: \".concat(errorData.error || \"未知错误\"));\n            }\n        } catch (error) {\n            alert(\"删除失败: \" + error.message);\n        }\n    };\n    const handleLogout = async ()=>{\n        try {\n            await fetch(\"/api/admin/auth\", {\n                method: \"DELETE\"\n            });\n            router.push(\"/admin/login\");\n        } catch (error) {\n            router.push(\"/admin/login\");\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleString(\"zh-CN\");\n    };\n    const getStatusBadge = (code)=>{\n        const usedCount = code.usedCount || 0;\n        const maxUsageCount = code.maxUsageCount || 1;\n        // 检查是否已达到使用次数上限\n        if (usedCount >= maxUsageCount) return {\n            text: \"已用完\",\n            class: \"used\"\n        };\n        if (!code.isActive) return {\n            text: \"已禁用\",\n            class: \"disabled\"\n        };\n        if (new Date() > new Date(code.expiresAt)) return {\n            text: \"已过期\",\n            class: \"expired\"\n        };\n        // 部分使用状态\n        if (usedCount > 0 && maxUsageCount > 1) return {\n            text: \"部分使用\",\n            class: \"partial\"\n        };\n        return {\n            text: \"可用\",\n            class: \"available\"\n        };\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"loading-container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null // 会被重定向到登录页\n        ;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: \"jsx-d10189405005aa32\",\n                    children: \"激活码管理 - Great Heights School\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-d10189405005aa32\" + \" \" + \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-d10189405005aa32\" + \" \" + \"main-card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                className: \"jsx-d10189405005aa32\" + \" \" + \"header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d10189405005aa32\" + \" \" + \"header-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d10189405005aa32\" + \" \" + \"header-left\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d10189405005aa32\" + \" \" + \"logo-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"logo-icon\",\n                                                        children: \"\\uD83C\\uDF93\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"title-section\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"jsx-d10189405005aa32\",\n                                                                children: \"激活码管理系统\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"subtitle\",\n                                                                children: \"Great Heights School\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d10189405005aa32\" + \" \" + \"header-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d10189405005aa32\" + \" \" + \"header-actions\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowCreateForm(true),\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"btn btn-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"16\",\n                                                                height: \"16\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"none\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"jsx-d10189405005aa32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M12 5v14m-7-7h14\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    className: \"jsx-d10189405005aa32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"生成激活码\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLogout,\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"btn btn-secondary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"16\",\n                                                                height: \"16\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"none\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"jsx-d10189405005aa32\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5z\",\n                                                                        fill: \"currentColor\",\n                                                                        className: \"jsx-d10189405005aa32\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z\",\n                                                                        fill: \"currentColor\",\n                                                                        className: \"jsx-d10189405005aa32\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 210,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"登出\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"jsx-d10189405005aa32\" + \" \" + \"main-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stats-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"jsx-d10189405005aa32\",\n                                                children: \"数据概览\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 222,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stats-grid\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-card\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-header\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-icon total\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"20\",\n                                                                            height: \"20\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"jsx-d10189405005aa32\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M9 11H7v8h2v-8zm4-4h-2v12h2V7zm4-2h-2v14h2V5z\",\n                                                                                fill: \"currentColor\",\n                                                                                className: \"jsx-d10189405005aa32\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 228,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 227,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 226,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"jsx-d10189405005aa32\",\n                                                                        children: \"总计\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-number\",\n                                                                children: stats.total || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-desc\",\n                                                                children: \"激活码总数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-card\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-header\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-icon available\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"20\",\n                                                                            height: \"20\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"jsx-d10189405005aa32\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z\",\n                                                                                fill: \"currentColor\",\n                                                                                className: \"jsx-d10189405005aa32\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 240,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 239,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 238,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"jsx-d10189405005aa32\",\n                                                                        children: \"可用\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-number available\",\n                                                                children: stats.available || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-desc\",\n                                                                children: \"可正常使用\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-card\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-header\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-icon used\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"20\",\n                                                                            height: \"20\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"jsx-d10189405005aa32\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\",\n                                                                                fill: \"currentColor\",\n                                                                                className: \"jsx-d10189405005aa32\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 252,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 251,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"jsx-d10189405005aa32\",\n                                                                        children: \"已使用\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-number used\",\n                                                                children: stats.used || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-desc\",\n                                                                children: \"已被激活\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-card\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-header\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-icon expired\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"20\",\n                                                                            height: \"20\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"jsx-d10189405005aa32\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-6V7h2v4h4v2z\",\n                                                                                fill: \"currentColor\",\n                                                                                className: \"jsx-d10189405005aa32\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 264,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 263,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"jsx-d10189405005aa32\",\n                                                                        children: \"已过期\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-number expired\",\n                                                                children: stats.expired || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-desc\",\n                                                                children: \"超过有效期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-card\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-header\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"stat-icon multi-use\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"20\",\n                                                                            height: \"20\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"jsx-d10189405005aa32\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\",\n                                                                                fill: \"currentColor\",\n                                                                                className: \"jsx-d10189405005aa32\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 276,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 275,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"jsx-d10189405005aa32\",\n                                                                        children: \"总使用次数\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-number multi-use\",\n                                                                children: stats.totalUsages || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"stat-desc\",\n                                                                children: \"累计激活次数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 223,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 221,\n                                        columnNumber: 11\n                                    }, this),\n                                    showCreateForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: ()=>setShowCreateForm(false),\n                                        className: \"jsx-d10189405005aa32\" + \" \" + \"modal-overlay\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: (e)=>e.stopPropagation(),\n                                            className: \"jsx-d10189405005aa32\" + \" \" + \"modal\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"jsx-d10189405005aa32\",\n                                                    children: \"生成新激活码\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleCreateCode,\n                                                    className: \"jsx-d10189405005aa32\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"form-group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: \"描述\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 293,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: newCodeForm.description,\n                                                                    onChange: (e)=>setNewCodeForm({\n                                                                            ...newCodeForm,\n                                                                            description: e.target.value\n                                                                        }),\n                                                                    placeholder: \"激活码用途描述（可选）\",\n                                                                    className: \"jsx-d10189405005aa32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"form-group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: \"有效期（天）\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: newCodeForm.expiresInDays,\n                                                                    onChange: (e)=>setNewCodeForm({\n                                                                            ...newCodeForm,\n                                                                            expiresInDays: parseInt(e.target.value)\n                                                                        }),\n                                                                    min: \"1\",\n                                                                    max: \"365\",\n                                                                    required: true,\n                                                                    className: \"jsx-d10189405005aa32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"form-group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: \"最大使用次数\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: newCodeForm.maxUsageCount,\n                                                                    onChange: (e)=>setNewCodeForm({\n                                                                            ...newCodeForm,\n                                                                            maxUsageCount: parseInt(e.target.value)\n                                                                        }),\n                                                                    min: \"1\",\n                                                                    max: \"1000\",\n                                                                    required: true,\n                                                                    className: \"jsx-d10189405005aa32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                    className: \"jsx-d10189405005aa32\" + \" \" + \"form-help\",\n                                                                    children: \"设置激活码可以被使用的最大次数（1表示一次性使用）\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"form-actions\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setShowCreateForm(false),\n                                                                    className: \"jsx-d10189405005aa32\" + \" \" + \"btn btn-secondary\",\n                                                                    children: \"取消\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"submit\",\n                                                                    disabled: isCreating,\n                                                                    className: \"jsx-d10189405005aa32\" + \" \" + \"btn btn-primary\",\n                                                                    children: isCreating ? \"生成中...\" : \"生成\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 289,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 288,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-d10189405005aa32\" + \" \" + \"codes-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d10189405005aa32\" + \" \" + \"section-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"jsx-d10189405005aa32\",\n                                                        children: \"激活码列表\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"section-actions\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"search-box\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    width: \"16\",\n                                                                    height: \"16\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    fill: \"none\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"m21 21-6-6m2-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0z\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"2\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        className: \"jsx-d10189405005aa32\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 351,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"搜索激活码...\",\n                                                                    className: \"jsx-d10189405005aa32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 346,\n                                                columnNumber: 13\n                                            }, this),\n                                            codes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d10189405005aa32\" + \" \" + \"empty-state\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"empty-icon\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"48\",\n                                                            height: \"48\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"jsx-d10189405005aa32\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M9 11H7v8h2v-8zm4-4h-2v12h2V7zm4-2h-2v14h2V5z\",\n                                                                fill: \"currentColor\",\n                                                                className: \"jsx-d10189405005aa32\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-d10189405005aa32\",\n                                                        children: \"暂无激活码\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-d10189405005aa32\",\n                                                        children: '点击\"生成激活码\"按钮创建第一个激活码'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d10189405005aa32\" + \" \" + \"codes-table\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"table-header\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"col-code\",\n                                                                children: \"激活码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"col-status\",\n                                                                children: \"状态\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"col-usage\",\n                                                                children: \"使用次数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"col-created\",\n                                                                children: \"创建时间\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"col-expires\",\n                                                                children: \"过期时间\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"col-user\",\n                                                                children: \"最后使用者\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d10189405005aa32\" + \" \" + \"col-actions\",\n                                                                children: \"操作\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    codes.map((code)=>{\n                                                        const status = getStatusBadge(code);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"table-row\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d10189405005aa32\" + \" \" + \"code-cell\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"code-text\",\n                                                                            children: code.code\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        code.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"code-desc\",\n                                                                            children: code.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-d10189405005aa32\" + \" \" + \"status-badge \".concat(status.class),\n                                                                        children: status.text\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d10189405005aa32\" + \" \" + \"usage-info\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"usage-count\",\n                                                                            children: [\n                                                                                code.usedCount || 0,\n                                                                                \" / \",\n                                                                                code.maxUsageCount || 1\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        (code.maxUsageCount || 1) > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"usage-type\",\n                                                                            children: \"多次使用\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 398,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: formatDate(code.createdAt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: formatDate(code.expiresAt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d10189405005aa32\",\n                                                                    children: code.usedBy || \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d10189405005aa32\" + \" \" + \"actions\",\n                                                                    children: [\n                                                                        (code.usedCount || 0) < (code.maxUsageCount || 1) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleToggleCode(code.id, code.isActive ? \"disable\" : \"enable\"),\n                                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"btn btn-sm \".concat(code.isActive ? \"btn-warning\" : \"btn-success\"),\n                                                                            children: code.isActive ? \"禁用\" : \"启用\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 406,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleDeleteCode(code.id),\n                                                                            className: \"jsx-d10189405005aa32\" + \" \" + \"btn btn-sm btn-danger\",\n                                                                            children: \"删除\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 413,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, code.id, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 19\n                                                        }, this);\n                                                    })\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 368,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 345,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-d10189405005aa32\" + \" \" + \"footer\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-d10189405005aa32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"jsx-d10189405005aa32\" + \" \" + \"powered-by\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"14\",\n                                        height: \"14\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"currentColor\",\n                                        className: \"jsx-d10189405005aa32\" + \" \" + \"icon\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\",\n                                            className: \"jsx-d10189405005aa32\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Powered by\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://www.ghs.red/\",\n                                        target: \"_blank\",\n                                        rel: \"noopener\",\n                                        className: \"jsx-d10189405005aa32\",\n                                        children: \"Garbage Human Studio\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                lineNumber: 432,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                        lineNumber: 430,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"d10189405005aa32\",\n                children: '.loading-container.jsx-d10189405005aa32{min-height:100vh;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;background:#fafafa;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Roboto\",\"Oxygen\",\"Ubuntu\",\"Cantarell\",\"Fira Sans\",\"Droid Sans\",\"Helvetica Neue\",sans-serif}.spinner.jsx-d10189405005aa32{width:32px;height:32px;border:3px solid#e1e5e9;border-top:3px solid#111827;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;-webkit-animation:spin.8s linear infinite;-moz-animation:spin.8s linear infinite;-o-animation:spin.8s linear infinite;animation:spin.8s linear infinite;margin-bottom:16px}@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}.container.jsx-d10189405005aa32{min-height:100vh;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;background:#fafafa;padding:20px;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Roboto\",\"Oxygen\",\"Ubuntu\",\"Cantarell\",\"Fira Sans\",\"Droid Sans\",\"Helvetica Neue\",sans-serif}.main-card.jsx-d10189405005aa32{background:#fff;max-width:1200px;width:100%;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;border:1px solid#e1e5e9;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1);overflow:hidden}.header.jsx-d10189405005aa32{background:#fff;border-bottom:1px solid#e1e5e9;padding:20px 32px}.header-content.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.header-left.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.header-right.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.logo-section.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:16px}.logo-icon.jsx-d10189405005aa32{font-size:32px;line-height:1}.title-section.jsx-d10189405005aa32 h1.jsx-d10189405005aa32{color:#111827;margin:0;font-size:24px;font-weight:600;letter-spacing:-.02em;line-height:1.2}.subtitle.jsx-d10189405005aa32{color:#6b7280;margin:2px 0 0 0;font-size:15px;font-weight:400;line-height:1.2}.header-actions.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px}.main-content.jsx-d10189405005aa32{padding:32px}.stats-section.jsx-d10189405005aa32{margin-bottom:48px}.stats-section.jsx-d10189405005aa32 h2.jsx-d10189405005aa32{color:#111827;font-size:20px;font-weight:600;margin-bottom:24px;letter-spacing:-.02em}.stats-grid.jsx-d10189405005aa32{display:grid;grid-template-columns:repeat(auto-fit,minmax(240px,1fr));gap:20px}.stat-card.jsx-d10189405005aa32{background:#fff;padding:24px;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;border:1px solid#e1e5e9;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1);-webkit-transition:all.2s ease-out;-moz-transition:all.2s ease-out;-o-transition:all.2s ease-out;transition:all.2s ease-out}.stat-card.jsx-d10189405005aa32:hover{border-color:#d1d5db;-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 4px 12px rgba(0,0,0,.08);-moz-box-shadow:0 4px 12px rgba(0,0,0,.08);box-shadow:0 4px 12px rgba(0,0,0,.08)}.stat-header.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:12px;margin-bottom:16px}.stat-icon.jsx-d10189405005aa32{width:40px;height:40px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.stat-icon.total.jsx-d10189405005aa32{background:#f3f4f6;color:#374151}.stat-icon.available.jsx-d10189405005aa32{background:#ecfdf5;color:#16a34a}.stat-icon.used.jsx-d10189405005aa32{background:#eff6ff;color:#2563eb}.stat-icon.expired.jsx-d10189405005aa32{background:#fef2f2;color:#dc2626}.stat-card.jsx-d10189405005aa32 h3.jsx-d10189405005aa32{margin:0;color:#374151;font-size:14px;font-weight:500}.stat-number.jsx-d10189405005aa32{font-size:28px;font-weight:700;color:#111827;margin-bottom:4px}.stat-desc.jsx-d10189405005aa32{color:#6b7280;font-size:13px;margin:0}.stat-number.available.jsx-d10189405005aa32{color:#16a34a}.stat-number.used.jsx-d10189405005aa32{color:#2563eb}.stat-number.expired.jsx-d10189405005aa32{color:#dc2626}.codes-section.jsx-d10189405005aa32{background:#fff;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;border:1px solid#e1e5e9;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1)}.section-header.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;padding:24px 24px 0 24px;margin-bottom:24px}.section-header.jsx-d10189405005aa32 h2.jsx-d10189405005aa32{margin:0;color:#111827;font-size:18px;font-weight:600;letter-spacing:-.02em}.section-actions.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px}.search-box.jsx-d10189405005aa32{position:relative;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.search-box.jsx-d10189405005aa32 svg.jsx-d10189405005aa32{position:absolute;left:12px;color:#9ca3af;z-index:1}.search-box.jsx-d10189405005aa32 input.jsx-d10189405005aa32{padding:8px 12px 8px 36px;border:1px solid#d1d5db;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;font-size:14px;width:200px;-webkit-transition:all.15s ease-out;-moz-transition:all.15s ease-out;-o-transition:all.15s ease-out;transition:all.15s ease-out}.search-box.jsx-d10189405005aa32 input.jsx-d10189405005aa32:focus{outline:none;border-color:#111827;-webkit-box-shadow:0 0 0 3px rgba(17,24,39,.1);-moz-box-shadow:0 0 0 3px rgba(17,24,39,.1);box-shadow:0 0 0 3px rgba(17,24,39,.1)}.empty-state.jsx-d10189405005aa32{text-align:center;padding:64px 24px}.empty-icon.jsx-d10189405005aa32{margin:0 auto 16px;width:48px;height:48px;color:#d1d5db}.empty-state.jsx-d10189405005aa32 h3.jsx-d10189405005aa32{color:#111827;font-size:16px;font-weight:600;margin:0 0 8px 0}.empty-state.jsx-d10189405005aa32 p.jsx-d10189405005aa32{color:#6b7280;font-size:14px;margin:0}.codes-table.jsx-d10189405005aa32{overflow-x:auto}.table-header.jsx-d10189405005aa32,.table-row.jsx-d10189405005aa32{display:grid;grid-template-columns:2fr 1fr 1.5fr 1.5fr 1.5fr 1fr;gap:16px;padding:16px 24px;border-bottom:1px solid#f3f4f6;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.table-header.jsx-d10189405005aa32{font-weight:600;color:#374151;background:#f9fafb;font-size:13px;text-transform:uppercase;letter-spacing:.05em;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}.code-cell.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:4px}.code-text.jsx-d10189405005aa32{font-family:monospace;font-weight:600;color:#333}.code-desc.jsx-d10189405005aa32{font-size:12px;color:#666}.status-badge.jsx-d10189405005aa32{padding:4px 8px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;font-size:12px;font-weight:500}.status-badge.available.jsx-d10189405005aa32{background:#d4edda;color:#155724}.status-badge.used.jsx-d10189405005aa32{background:#e2e3e5;color:#383d41}.status-badge.disabled.jsx-d10189405005aa32{background:#f8d7da;color:#721c24}.status-badge.expired.jsx-d10189405005aa32{background:#fff3cd;color:#856404}.actions.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:8px}.btn.jsx-d10189405005aa32{display:-webkit-inline-box;display:-webkit-inline-flex;display:-moz-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:6px;padding:8px 16px;border:1px solid transparent;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;cursor:pointer;font-size:14px;font-weight:500;-webkit-transition:all.15s ease-out;-moz-transition:all.15s ease-out;-o-transition:all.15s ease-out;transition:all.15s ease-out;text-decoration:none}.btn-primary.jsx-d10189405005aa32{background:#111827;color:#fff;border-color:#111827}.btn-primary.jsx-d10189405005aa32:hover{background:#374151;border-color:#374151;-webkit-transform:translatey(-1px);-moz-transform:translatey(-1px);-ms-transform:translatey(-1px);-o-transform:translatey(-1px);transform:translatey(-1px);-webkit-box-shadow:0 2px 4px rgba(0,0,0,.1);-moz-box-shadow:0 2px 4px rgba(0,0,0,.1);box-shadow:0 2px 4px rgba(0,0,0,.1)}.btn-secondary.jsx-d10189405005aa32{background:#fff;color:#374151;border-color:#d1d5db}.btn-secondary.jsx-d10189405005aa32:hover{background:#f9fafb;border-color:#9ca3af;-webkit-transform:translatey(-1px);-moz-transform:translatey(-1px);-ms-transform:translatey(-1px);-o-transform:translatey(-1px);transform:translatey(-1px);-webkit-box-shadow:0 2px 4px rgba(0,0,0,.05);-moz-box-shadow:0 2px 4px rgba(0,0,0,.05);box-shadow:0 2px 4px rgba(0,0,0,.05)}.btn-success.jsx-d10189405005aa32{background:#16a34a;color:#fff;border-color:#16a34a}.btn-success.jsx-d10189405005aa32:hover{background:#15803d;border-color:#15803d}.btn-warning.jsx-d10189405005aa32{background:#f59e0b;color:#fff;border-color:#f59e0b}.btn-warning.jsx-d10189405005aa32:hover{background:#d97706;border-color:#d97706}.btn-danger.jsx-d10189405005aa32{background:#dc2626;color:#fff;border-color:#dc2626}.btn-danger.jsx-d10189405005aa32:hover{background:#b91c1c;border-color:#b91c1c}.btn-sm.jsx-d10189405005aa32{padding:4px 8px;font-size:12px}.modal-overlay.jsx-d10189405005aa32{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.5);display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;z-index:1000}.modal.jsx-d10189405005aa32{background:#fff;padding:24px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;width:90%;max-width:400px}.modal.jsx-d10189405005aa32 h2.jsx-d10189405005aa32{margin:0 0 20px 0;color:#333}.form-group.jsx-d10189405005aa32{margin-bottom:16px}.form-group.jsx-d10189405005aa32 label.jsx-d10189405005aa32{display:block;margin-bottom:4px;color:#333;font-weight:500}.form-group.jsx-d10189405005aa32 input.jsx-d10189405005aa32{width:100%;padding:8px 12px;border:1px solid#ddd;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;font-size:14px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.form-group.jsx-d10189405005aa32 input.jsx-d10189405005aa32:focus{outline:none;border-color:#4285f4}.form-actions.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px;-webkit-box-pack:end;-webkit-justify-content:flex-end;-moz-box-pack:end;-ms-flex-pack:end;justify-content:flex-end}@media(max-width:768px){.header-content.jsx-d10189405005aa32{padding:12px 16px;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px;-webkit-box-align:stretch;-webkit-align-items:stretch;-moz-box-align:stretch;-ms-flex-align:stretch;align-items:stretch}.logo.jsx-d10189405005aa32{-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.header-actions.jsx-d10189405005aa32{-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.main-content.jsx-d10189405005aa32{padding:16px}.stats-grid.jsx-d10189405005aa32{grid-template-columns:1fr;gap:16px}.section-header.jsx-d10189405005aa32{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px;-webkit-box-align:stretch;-webkit-align-items:stretch;-moz-box-align:stretch;-ms-flex-align:stretch;align-items:stretch;padding:16px 16px 0 16px}.search-box.jsx-d10189405005aa32 input.jsx-d10189405005aa32{width:100%}.table-header.jsx-d10189405005aa32,.table-row.jsx-d10189405005aa32{grid-template-columns:1fr;gap:8px;padding:12px 16px}.table-header.jsx-d10189405005aa32>div.jsx-d10189405005aa32,.table-row.jsx-d10189405005aa32>div.jsx-d10189405005aa32{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.table-header.jsx-d10189405005aa32>div.jsx-d10189405005aa32::before,.table-row.jsx-d10189405005aa32>div.jsx-d10189405005aa32::before{content:attr(data-label);font-weight:600;color:#6b7280;font-size:12px}.col-code.jsx-d10189405005aa32::before{content:\"激活码\"}.col-status.jsx-d10189405005aa32::before{content:\"状态\"}.col-created.jsx-d10189405005aa32::before{content:\"创建时间\"}.col-expires.jsx-d10189405005aa32::before{content:\"过期时间\"}.col-user.jsx-d10189405005aa32::before{content:\"使用者\"}.col-actions.jsx-d10189405005aa32::before{content:\"操作\"}}.footer.jsx-d10189405005aa32{margin-top:32px;color:#9ca3af;font-size:13px;text-align:center}.powered-by.jsx-d10189405005aa32{display:-webkit-inline-box;display:-webkit-inline-flex;display:-moz-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:4px}.icon.jsx-d10189405005aa32{-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.footer.jsx-d10189405005aa32 a.jsx-d10189405005aa32{color:#6b7280;text-decoration:none;font-weight:500;margin-left:4px}.footer.jsx-d10189405005aa32 a.jsx-d10189405005aa32:hover{color:#374151;text-decoration:underline}@media(max-width:1023px){.main-card.jsx-d10189405005aa32{max-width:100%;margin:0;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0;border-left:none;border-right:none}.container.jsx-d10189405005aa32{padding:0;-webkit-box-pack:start;-webkit-justify-content:flex-start;-moz-box-pack:start;-ms-flex-pack:start;justify-content:flex-start}}@media(max-width:768px){.header.jsx-d10189405005aa32{padding:16px 20px}.header-content.jsx-d10189405005aa32{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start}.header-actions.jsx-d10189405005aa32{width:100%;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}.main-content.jsx-d10189405005aa32{padding:20px}.logo-icon.jsx-d10189405005aa32{font-size:24px}.title-section.jsx-d10189405005aa32 h1.jsx-d10189405005aa32{font-size:20px}.subtitle.jsx-d10189405005aa32{font-size:13px}}@media(max-width:480px){.header.jsx-d10189405005aa32{padding:12px 16px}.main-content.jsx-d10189405005aa32{padding:16px}.stats-grid.jsx-d10189405005aa32{grid-template-columns:1fr;gap:16px}.stat-card.jsx-d10189405005aa32{padding:20px}.btn.jsx-d10189405005aa32{padding:8px 12px;font-size:13px}.header-actions.jsx-d10189405005aa32{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:8px}}'\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_s(AdminDashboard, \"uIbil0a0AFkky75jTT0t5XOmli0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/admin/dashboard.js\n"));

/***/ })

});