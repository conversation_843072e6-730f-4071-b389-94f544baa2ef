"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/dashboard",{

/***/ "./pages/admin/dashboard.js":
/*!**********************************!*\
  !*** ./pages/admin/dashboard.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [codes, setCodes] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"codes\") // 'codes' 或 'users'\n    ;\n    const [showCreateForm, setShowCreateForm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [newCodeForm, setNewCodeForm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        description: \"\",\n        expiresInDays: 30,\n        maxUsageCount: 1\n    });\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // 检查认证状态\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        checkAuth();\n    }, []);\n    // 加载激活码数据\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (isAuthenticated) {\n            loadActivationCodes();\n            loadUsers();\n        }\n    }, [\n        isAuthenticated\n    ]);\n    const checkAuth = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/auth\");\n            const result = await response.json();\n            if (result.authenticated) {\n                setIsAuthenticated(true);\n            } else {\n                router.push(\"/admin/login\");\n            }\n        } catch (error) {\n            router.push(\"/admin/login\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loadActivationCodes = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/activation-codes\");\n            if (response.ok) {\n                const data = await response.json();\n                setCodes(data.codes);\n                setStats(data.stats);\n            }\n        } catch (error) {\n            console.error(\"Failed to load activation codes:\", error);\n        }\n    };\n    const loadUsers = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/users\");\n            if (response.ok) {\n                const data = await response.json();\n                setUsers(data.users);\n            }\n        } catch (error) {\n            console.error(\"Failed to load users:\", error);\n        }\n    };\n    const handleCreateCode = async (e)=>{\n        e.preventDefault();\n        setIsCreating(true);\n        try {\n            const response = await fetch(\"/api/admin/activation-codes\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newCodeForm)\n            });\n            if (response.ok) {\n                setNewCodeForm({\n                    description: \"\",\n                    expiresInDays: 30,\n                    maxUsageCount: 1\n                });\n                setShowCreateForm(false);\n                loadActivationCodes() // 重新加载数据\n                ;\n                loadUsers() // 重新加载用户数据\n                ;\n            } else {\n                alert(\"创建激活码失败\");\n            }\n        } catch (error) {\n            alert(\"创建激活码失败\");\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const handleToggleCode = async (codeId, action)=>{\n        try {\n            const response = await fetch(\"/api/admin/activation-codes\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    codeId,\n                    action\n                })\n            });\n            if (response.ok) {\n                loadActivationCodes() // 重新加载数据\n                ;\n                loadUsers() // 重新加载用户数据\n                ;\n            } else {\n                alert(\"操作失败\");\n            }\n        } catch (error) {\n            alert(\"操作失败\");\n        }\n    };\n    const handleDeleteCode = async (codeId)=>{\n        if (!confirm(\"确定要删除这个激活码吗？\")) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/admin/activation-codes?codeId=\".concat(encodeURIComponent(codeId)), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                loadActivationCodes() // 重新加载数据\n                ;\n                loadUsers() // 重新加载用户数据\n                ;\n            } else {\n                const errorData = await response.json().catch(()=>({}));\n                alert(\"删除失败: \".concat(errorData.error || \"未知错误\"));\n            }\n        } catch (error) {\n            alert(\"删除失败: \" + error.message);\n        }\n    };\n    const handleLogout = async ()=>{\n        try {\n            await fetch(\"/api/admin/auth\", {\n                method: \"DELETE\"\n            });\n            router.push(\"/admin/login\");\n        } catch (error) {\n            router.push(\"/admin/login\");\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleString(\"zh-CN\");\n    };\n    const getStatusBadge = (code)=>{\n        const usedCount = code.usedCount || 0;\n        const maxUsageCount = code.maxUsageCount || 1;\n        // 检查是否已达到使用次数上限\n        if (usedCount >= maxUsageCount) return {\n            text: \"已用完\",\n            class: \"used\"\n        };\n        if (!code.isActive) return {\n            text: \"已禁用\",\n            class: \"disabled\"\n        };\n        if (new Date() > new Date(code.expiresAt)) return {\n            text: \"已过期\",\n            class: \"expired\"\n        };\n        // 部分使用状态\n        if (usedCount > 0 && maxUsageCount > 1) return {\n            text: \"部分使用\",\n            class: \"partial\"\n        };\n        return {\n            text: \"可用\",\n            class: \"available\"\n        };\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"loading-container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null // 会被重定向到登录页\n        ;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: \"jsx-f410bf512bcf7983\",\n                    children: \"激活码管理 - Great Heights School\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-f410bf512bcf7983\" + \" \" + \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"main-card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"header-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-f410bf512bcf7983\" + \" \" + \"header-left\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"logo-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"logo-icon\",\n                                                        children: \"\\uD83C\\uDF93\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"title-section\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"jsx-f410bf512bcf7983\",\n                                                                children: \"激活码管理系统\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"subtitle\",\n                                                                children: \"Great Heights School\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-f410bf512bcf7983\" + \" \" + \"header-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"header-actions\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowCreateForm(true),\n                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"btn btn-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"16\",\n                                                                height: \"16\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"none\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"jsx-f410bf512bcf7983\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M12 5v14m-7-7h14\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    className: \"jsx-f410bf512bcf7983\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"生成激活码\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLogout,\n                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"btn btn-secondary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"16\",\n                                                                height: \"16\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"none\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"jsx-f410bf512bcf7983\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5z\",\n                                                                        fill: \"currentColor\",\n                                                                        className: \"jsx-f410bf512bcf7983\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z\",\n                                                                        fill: \"currentColor\",\n                                                                        className: \"jsx-f410bf512bcf7983\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 228,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"登出\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"main-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"tabs-navigation\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"codes\"),\n                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"tab-button \".concat(activeTab === \"codes\" ? \"active\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"jsx-f410bf512bcf7983\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9 11H7v8h2v-8zm4-4h-2v12h2V7zm4-2h-2v14h2V5z\",\n                                                            fill: \"currentColor\",\n                                                            className: \"jsx-f410bf512bcf7983\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"激活码管理\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 240,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"users\"),\n                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"tab-button \".concat(activeTab === \"users\" ? \"active\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"jsx-f410bf512bcf7983\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                className: \"jsx-f410bf512bcf7983\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"9\",\n                                                                cy: \"7\",\n                                                                r: \"4\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                className: \"jsx-f410bf512bcf7983\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"m22 21-3-3m0 0a2 2 0 1 0-4 0 2 2 0 0 0 4 0z\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                className: \"jsx-f410bf512bcf7983\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"用户列表\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    activeTab === \"codes\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"stats-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"jsx-f410bf512bcf7983\",\n                                                        children: \"数据概览\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stats-grid\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-header\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-icon total\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    fill: \"none\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    className: \"jsx-f410bf512bcf7983\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M9 11H7v8h2v-8zm4-4h-2v12h2V7zm4-2h-2v14h2V5z\",\n                                                                                        fill: \"currentColor\",\n                                                                                        className: \"jsx-f410bf512bcf7983\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                        lineNumber: 272,\n                                                                                        columnNumber: 23\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 271,\n                                                                                    columnNumber: 21\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 270,\n                                                                                columnNumber: 19\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-f410bf512bcf7983\",\n                                                                                children: \"总计\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 275,\n                                                                                columnNumber: 19\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-number\",\n                                                                        children: stats.total || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-desc\",\n                                                                        children: \"激活码总数\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-header\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-icon available\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    fill: \"none\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    className: \"jsx-f410bf512bcf7983\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z\",\n                                                                                        fill: \"currentColor\",\n                                                                                        className: \"jsx-f410bf512bcf7983\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                        lineNumber: 284,\n                                                                                        columnNumber: 23\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 283,\n                                                                                    columnNumber: 21\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 282,\n                                                                                columnNumber: 19\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-f410bf512bcf7983\",\n                                                                                children: \"可用\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 19\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-number available\",\n                                                                        children: stats.available || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-desc\",\n                                                                        children: \"可正常使用\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-header\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-icon used\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    fill: \"none\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    className: \"jsx-f410bf512bcf7983\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\",\n                                                                                        fill: \"currentColor\",\n                                                                                        className: \"jsx-f410bf512bcf7983\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                        lineNumber: 296,\n                                                                                        columnNumber: 23\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 295,\n                                                                                    columnNumber: 21\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 294,\n                                                                                columnNumber: 19\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-f410bf512bcf7983\",\n                                                                                children: \"已使用\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 19\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-number used\",\n                                                                        children: stats.used || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-desc\",\n                                                                        children: \"已被激活\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-header\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-icon expired\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    fill: \"none\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    className: \"jsx-f410bf512bcf7983\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-6V7h2v4h4v2z\",\n                                                                                        fill: \"currentColor\",\n                                                                                        className: \"jsx-f410bf512bcf7983\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                        lineNumber: 308,\n                                                                                        columnNumber: 23\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 21\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 19\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-f410bf512bcf7983\",\n                                                                                children: \"已过期\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 311,\n                                                                                columnNumber: 19\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-number expired\",\n                                                                        children: stats.expired || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-desc\",\n                                                                        children: \"超过有效期\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-header\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-icon multi-use\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    fill: \"none\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    className: \"jsx-f410bf512bcf7983\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\",\n                                                                                        fill: \"currentColor\",\n                                                                                        className: \"jsx-f410bf512bcf7983\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                        lineNumber: 320,\n                                                                                        columnNumber: 23\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 319,\n                                                                                    columnNumber: 21\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 318,\n                                                                                columnNumber: 19\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-f410bf512bcf7983\",\n                                                                                children: \"总使用次数\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 323,\n                                                                                columnNumber: 19\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-number multi-use\",\n                                                                        children: stats.totalUsages || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"stat-desc\",\n                                                                        children: \"累计激活次数\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 13\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            showCreateForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: ()=>setShowCreateForm(false),\n                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"modal-overlay\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    onClick: (e)=>e.stopPropagation(),\n                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"modal\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"jsx-f410bf512bcf7983\",\n                                                            children: \"生成新激活码\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                            onSubmit: handleCreateCode,\n                                                            className: \"jsx-f410bf512bcf7983\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"form-group\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-f410bf512bcf7983\",\n                                                                            children: \"描述\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 19\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: newCodeForm.description,\n                                                                            onChange: (e)=>setNewCodeForm({\n                                                                                    ...newCodeForm,\n                                                                                    description: e.target.value\n                                                                                }),\n                                                                            placeholder: \"激活码用途描述（可选）\",\n                                                                            className: \"jsx-f410bf512bcf7983\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 19\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"form-group\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-f410bf512bcf7983\",\n                                                                            children: \"有效期（天）\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 346,\n                                                                            columnNumber: 19\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            value: newCodeForm.expiresInDays,\n                                                                            onChange: (e)=>setNewCodeForm({\n                                                                                    ...newCodeForm,\n                                                                                    expiresInDays: parseInt(e.target.value)\n                                                                                }),\n                                                                            min: \"1\",\n                                                                            max: \"365\",\n                                                                            required: true,\n                                                                            className: \"jsx-f410bf512bcf7983\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 19\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"form-group\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-f410bf512bcf7983\",\n                                                                            children: \"最大使用次数\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 19\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            value: newCodeForm.maxUsageCount,\n                                                                            onChange: (e)=>setNewCodeForm({\n                                                                                    ...newCodeForm,\n                                                                                    maxUsageCount: parseInt(e.target.value)\n                                                                                }),\n                                                                            min: \"1\",\n                                                                            max: \"1000\",\n                                                                            required: true,\n                                                                            className: \"jsx-f410bf512bcf7983\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 358,\n                                                                            columnNumber: 19\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                            className: \"jsx-f410bf512bcf7983\" + \" \" + \"form-help\",\n                                                                            children: \"设置激活码可以被使用的最大次数（1表示一次性使用）\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 366,\n                                                                            columnNumber: 19\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"form-actions\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowCreateForm(false),\n                                                                            className: \"jsx-f410bf512bcf7983\" + \" \" + \"btn btn-secondary\",\n                                                                            children: \"取消\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 19\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"submit\",\n                                                                            disabled: isCreating,\n                                                                            className: \"jsx-f410bf512bcf7983\" + \" \" + \"btn btn-primary\",\n                                                                            children: isCreating ? \"生成中...\" : \"生成\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 19\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 13\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 332,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"codes-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"section-header\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"jsx-f410bf512bcf7983\",\n                                                                children: \"激活码列表\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"section-actions\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"search-box\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"jsx-f410bf512bcf7983\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"m21 21-6-6m2-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0z\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"2\",\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                className: \"jsx-f410bf512bcf7983\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 395,\n                                                                                columnNumber: 21\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 19\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"搜索激活码...\",\n                                                                            className: \"jsx-f410bf512bcf7983\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 19\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    codes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"empty-state\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"empty-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    width: \"48\",\n                                                                    height: \"48\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    fill: \"none\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"jsx-f410bf512bcf7983\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M9 11H7v8h2v-8zm4-4h-2v12h2V7zm4-2h-2v14h2V5z\",\n                                                                        fill: \"currentColor\",\n                                                                        className: \"jsx-f410bf512bcf7983\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"jsx-f410bf512bcf7983\",\n                                                                children: \"暂无激活码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-f410bf512bcf7983\",\n                                                                children: '点击\"生成激活码\"按钮创建第一个激活码'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 15\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"codes-table\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"table-header\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"col-code\",\n                                                                        children: \"激活码\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"col-status\",\n                                                                        children: \"状态\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"col-usage\",\n                                                                        children: \"使用次数\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"col-created\",\n                                                                        children: \"创建时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"col-expires\",\n                                                                        children: \"过期时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"col-user\",\n                                                                        children: \"最后使用者\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"col-actions\",\n                                                                        children: \"操作\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            codes.map((code)=>{\n                                                                const status = getStatusBadge(code);\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"table-row\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-f410bf512bcf7983\" + \" \" + \"code-cell\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"code-text\",\n                                                                                    children: code.code\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 427,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                code.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"code-desc\",\n                                                                                    children: code.description\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 429,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 426,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-f410bf512bcf7983\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"status-badge \".concat(status.class),\n                                                                                children: status.text\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 432,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-f410bf512bcf7983\" + \" \" + \"usage-info\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"usage-count\",\n                                                                                    children: [\n                                                                                        code.usedCount || 0,\n                                                                                        \" / \",\n                                                                                        code.maxUsageCount || 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 438,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                (code.maxUsageCount || 1) > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"usage-type\",\n                                                                                    children: \"多次使用\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 442,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 437,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-f410bf512bcf7983\",\n                                                                            children: formatDate(code.createdAt)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 445,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-f410bf512bcf7983\",\n                                                                            children: formatDate(code.expiresAt)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-f410bf512bcf7983\",\n                                                                            children: code.usedBy || \"-\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 447,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-f410bf512bcf7983\" + \" \" + \"actions\",\n                                                                            children: [\n                                                                                (code.usedCount || 0) < (code.maxUsageCount || 1) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleToggleCode(code.id, code.isActive ? \"disable\" : \"enable\"),\n                                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"btn btn-sm \".concat(code.isActive ? \"btn-warning\" : \"btn-success\"),\n                                                                                    children: code.isActive ? \"禁用\" : \"启用\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleDeleteCode(code.id),\n                                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"btn btn-sm btn-danger\",\n                                                                                    children: \"删除\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 457,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, code.id, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 19\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 389,\n                                                columnNumber: 11\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    activeTab === \"users\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"users-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"section-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"jsx-f410bf512bcf7983\",\n                                                        children: \"用户列表\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"section-info\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-f410bf512bcf7983\" + \" \" + \"info-text\",\n                                                            children: [\n                                                                \"共 \",\n                                                                users.length,\n                                                                \" 个用户\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this),\n                                            users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"empty-state\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"empty-icon\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"48\",\n                                                            height: \"48\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"jsx-f410bf512bcf7983\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    className: \"jsx-f410bf512bcf7983\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"9\",\n                                                                    cy: \"7\",\n                                                                    r: \"4\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    className: \"jsx-f410bf512bcf7983\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-f410bf512bcf7983\",\n                                                        children: \"暂无用户\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-f410bf512bcf7983\",\n                                                        children: \"还没有用户使用激活码注册\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"users-table\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"table-header\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"col-email\",\n                                                                children: \"用户邮箱\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"col-code\",\n                                                                children: \"使用的激活码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"col-description\",\n                                                                children: \"激活码描述\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"col-usage-info\",\n                                                                children: \"使用情况\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"col-used-time\",\n                                                                children: \"使用时间\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    users.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-f410bf512bcf7983\" + \" \" + \"table-row\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"email-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"email-text\",\n                                                                        children: user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"code-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"code-text\",\n                                                                        children: user.activationCode\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 509,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"description-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"description-text\",\n                                                                        children: user.codeDescription\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"usage-cell\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-f410bf512bcf7983\" + \" \" + \"usage-count\",\n                                                                            children: [\n                                                                                user.currentUsedCount,\n                                                                                \" / \",\n                                                                                user.maxUsageCount\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 515,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        user.maxUsageCount > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-f410bf512bcf7983\" + \" \" + \"usage-type\",\n                                                                            children: \"多次使用\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 519,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f410bf512bcf7983\" + \" \" + \"time-cell\",\n                                                                    children: formatDate(user.usedAt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, \"\".concat(user.email, \"-\").concat(user.activationCode, \"-\").concat(index), true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 495,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"footer\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-f410bf512bcf7983\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"jsx-f410bf512bcf7983\" + \" \" + \"powered-by\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"14\",\n                                        height: \"14\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"currentColor\",\n                                        className: \"jsx-f410bf512bcf7983\" + \" \" + \"icon\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\",\n                                            className: \"jsx-f410bf512bcf7983\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 539,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 538,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Powered by\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://www.ghs.red/\",\n                                        target: \"_blank\",\n                                        rel: \"noopener\",\n                                        className: \"jsx-f410bf512bcf7983\",\n                                        children: \"Garbage Human Studio\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                lineNumber: 537,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                            lineNumber: 536,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                        lineNumber: 535,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"f410bf512bcf7983\",\n                children: '.loading-container.jsx-f410bf512bcf7983{min-height:100vh;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;background:#fafafa;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Roboto\",\"Oxygen\",\"Ubuntu\",\"Cantarell\",\"Fira Sans\",\"Droid Sans\",\"Helvetica Neue\",sans-serif}.spinner.jsx-f410bf512bcf7983{width:32px;height:32px;border:3px solid#e1e5e9;border-top:3px solid#111827;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;-webkit-animation:spin.8s linear infinite;-moz-animation:spin.8s linear infinite;-o-animation:spin.8s linear infinite;animation:spin.8s linear infinite;margin-bottom:16px}@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}.container.jsx-f410bf512bcf7983{min-height:100vh;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;background:#fafafa;padding:20px;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Roboto\",\"Oxygen\",\"Ubuntu\",\"Cantarell\",\"Fira Sans\",\"Droid Sans\",\"Helvetica Neue\",sans-serif}.main-card.jsx-f410bf512bcf7983{background:#fff;max-width:1200px;width:100%;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;border:1px solid#e1e5e9;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1);overflow:hidden}.header.jsx-f410bf512bcf7983{background:#fff;border-bottom:1px solid#e1e5e9;padding:20px 32px}.header-content.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.header-left.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.header-right.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.logo-section.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:16px}.logo-icon.jsx-f410bf512bcf7983{font-size:32px;line-height:1}.title-section.jsx-f410bf512bcf7983 h1.jsx-f410bf512bcf7983{color:#111827;margin:0;font-size:24px;font-weight:600;letter-spacing:-.02em;line-height:1.2}.subtitle.jsx-f410bf512bcf7983{color:#6b7280;margin:2px 0 0 0;font-size:15px;font-weight:400;line-height:1.2}.header-actions.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px}.main-content.jsx-f410bf512bcf7983{padding:32px}.tabs-navigation.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;border-bottom:2px solid#e1e5e9;margin-bottom:32px;gap:4px}.tab-button.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:8px;padding:12px 20px;border:none;background:transparent;color:#6b7280;font-size:14px;font-weight:500;cursor:pointer;-webkit-border-radius:8px 8px 0 0;-moz-border-radius:8px 8px 0 0;border-radius:8px 8px 0 0;-webkit-transition:all.2s ease;-moz-transition:all.2s ease;-o-transition:all.2s ease;transition:all.2s ease;position:relative}.tab-button.jsx-f410bf512bcf7983:hover{background:#f3f4f6;color:#374151}.tab-button.active.jsx-f410bf512bcf7983{background:#fff;color:#111827;border-bottom:2px solid#2563eb;margin-bottom:-2px}.tab-button.jsx-f410bf512bcf7983 svg.jsx-f410bf512bcf7983{width:16px;height:16px}.stats-section.jsx-f410bf512bcf7983{margin-bottom:48px}.stats-section.jsx-f410bf512bcf7983 h2.jsx-f410bf512bcf7983{color:#111827;font-size:20px;font-weight:600;margin-bottom:24px;letter-spacing:-.02em}.stats-grid.jsx-f410bf512bcf7983{display:grid;grid-template-columns:repeat(auto-fit,minmax(240px,1fr));gap:20px}.stat-card.jsx-f410bf512bcf7983{background:#fff;padding:24px;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;border:1px solid#e1e5e9;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1);-webkit-transition:all.2s ease-out;-moz-transition:all.2s ease-out;-o-transition:all.2s ease-out;transition:all.2s ease-out}.stat-card.jsx-f410bf512bcf7983:hover{border-color:#d1d5db;-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 4px 12px rgba(0,0,0,.08);-moz-box-shadow:0 4px 12px rgba(0,0,0,.08);box-shadow:0 4px 12px rgba(0,0,0,.08)}.stat-header.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:12px;margin-bottom:16px}.stat-icon.jsx-f410bf512bcf7983{width:40px;height:40px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.stat-icon.total.jsx-f410bf512bcf7983{background:#f3f4f6;color:#374151}.stat-icon.available.jsx-f410bf512bcf7983{background:#ecfdf5;color:#16a34a}.stat-icon.used.jsx-f410bf512bcf7983{background:#eff6ff;color:#2563eb}.stat-icon.expired.jsx-f410bf512bcf7983{background:#fef2f2;color:#dc2626}.stat-icon.multi-use.jsx-f410bf512bcf7983{background:#fef3c7;color:#d97706}.stat-card.jsx-f410bf512bcf7983 h3.jsx-f410bf512bcf7983{margin:0;color:#374151;font-size:14px;font-weight:500}.stat-number.jsx-f410bf512bcf7983{font-size:28px;font-weight:700;color:#111827;margin-bottom:4px}.stat-desc.jsx-f410bf512bcf7983{color:#6b7280;font-size:13px;margin:0}.stat-number.available.jsx-f410bf512bcf7983{color:#16a34a}.stat-number.used.jsx-f410bf512bcf7983{color:#2563eb}.stat-number.expired.jsx-f410bf512bcf7983{color:#dc2626}.codes-section.jsx-f410bf512bcf7983{background:#fff;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;border:1px solid#e1e5e9;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1)}.section-header.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;padding:24px 24px 0 24px;margin-bottom:24px}.section-header.jsx-f410bf512bcf7983 h2.jsx-f410bf512bcf7983{margin:0;color:#111827;font-size:18px;font-weight:600;letter-spacing:-.02em}.section-actions.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px}.search-box.jsx-f410bf512bcf7983{position:relative;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.search-box.jsx-f410bf512bcf7983 svg.jsx-f410bf512bcf7983{position:absolute;left:12px;color:#9ca3af;z-index:1}.search-box.jsx-f410bf512bcf7983 input.jsx-f410bf512bcf7983{padding:8px 12px 8px 36px;border:1px solid#d1d5db;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;font-size:14px;width:200px;-webkit-transition:all.15s ease-out;-moz-transition:all.15s ease-out;-o-transition:all.15s ease-out;transition:all.15s ease-out}.search-box.jsx-f410bf512bcf7983 input.jsx-f410bf512bcf7983:focus{outline:none;border-color:#111827;-webkit-box-shadow:0 0 0 3px rgba(17,24,39,.1);-moz-box-shadow:0 0 0 3px rgba(17,24,39,.1);box-shadow:0 0 0 3px rgba(17,24,39,.1)}.empty-state.jsx-f410bf512bcf7983{text-align:center;padding:64px 24px}.empty-icon.jsx-f410bf512bcf7983{margin:0 auto 16px;width:48px;height:48px;color:#d1d5db}.empty-state.jsx-f410bf512bcf7983 h3.jsx-f410bf512bcf7983{color:#111827;font-size:16px;font-weight:600;margin:0 0 8px 0}.empty-state.jsx-f410bf512bcf7983 p.jsx-f410bf512bcf7983{color:#6b7280;font-size:14px;margin:0}.codes-table.jsx-f410bf512bcf7983{overflow-x:auto}.table-header.jsx-f410bf512bcf7983,.table-row.jsx-f410bf512bcf7983{display:grid;grid-template-columns:2fr 1fr 1.5fr 1.5fr 1.5fr 1fr;gap:16px;padding:16px 24px;border-bottom:1px solid#f3f4f6;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.table-header.jsx-f410bf512bcf7983{font-weight:600;color:#374151;background:#f9fafb;font-size:13px;text-transform:uppercase;letter-spacing:.05em;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}.code-cell.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:4px}.code-text.jsx-f410bf512bcf7983{font-family:monospace;font-weight:600;color:#333}.code-desc.jsx-f410bf512bcf7983{font-size:12px;color:#666}.status-badge.jsx-f410bf512bcf7983{padding:4px 8px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;font-size:12px;font-weight:500}.status-badge.available.jsx-f410bf512bcf7983{background:#d4edda;color:#155724}.status-badge.used.jsx-f410bf512bcf7983{background:#e2e3e5;color:#383d41}.status-badge.disabled.jsx-f410bf512bcf7983{background:#f8d7da;color:#721c24}.status-badge.expired.jsx-f410bf512bcf7983{background:#fff3cd;color:#856404}.status-badge.partial.jsx-f410bf512bcf7983{background:#e0f2fe;color:#0277bd}.usage-info.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:4px}.usage-count.jsx-f410bf512bcf7983{font-weight:600;color:#374151}.usage-type.jsx-f410bf512bcf7983{font-size:11px;color:#6b7280;background:#f3f4f6;padding:2px 6px;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px;-webkit-align-self:flex-start;-ms-flex-item-align:start;align-self:flex-start}.form-help.jsx-f410bf512bcf7983{font-size:12px;color:#6b7280;margin-top:4px;display:block}.actions.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:8px}.btn.jsx-f410bf512bcf7983{display:-webkit-inline-box;display:-webkit-inline-flex;display:-moz-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:6px;padding:8px 16px;border:1px solid transparent;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;cursor:pointer;font-size:14px;font-weight:500;-webkit-transition:all.15s ease-out;-moz-transition:all.15s ease-out;-o-transition:all.15s ease-out;transition:all.15s ease-out;text-decoration:none}.btn-primary.jsx-f410bf512bcf7983{background:#111827;color:#fff;border-color:#111827}.btn-primary.jsx-f410bf512bcf7983:hover{background:#374151;border-color:#374151;-webkit-transform:translatey(-1px);-moz-transform:translatey(-1px);-ms-transform:translatey(-1px);-o-transform:translatey(-1px);transform:translatey(-1px);-webkit-box-shadow:0 2px 4px rgba(0,0,0,.1);-moz-box-shadow:0 2px 4px rgba(0,0,0,.1);box-shadow:0 2px 4px rgba(0,0,0,.1)}.btn-secondary.jsx-f410bf512bcf7983{background:#fff;color:#374151;border-color:#d1d5db}.btn-secondary.jsx-f410bf512bcf7983:hover{background:#f9fafb;border-color:#9ca3af;-webkit-transform:translatey(-1px);-moz-transform:translatey(-1px);-ms-transform:translatey(-1px);-o-transform:translatey(-1px);transform:translatey(-1px);-webkit-box-shadow:0 2px 4px rgba(0,0,0,.05);-moz-box-shadow:0 2px 4px rgba(0,0,0,.05);box-shadow:0 2px 4px rgba(0,0,0,.05)}.btn-success.jsx-f410bf512bcf7983{background:#16a34a;color:#fff;border-color:#16a34a}.btn-success.jsx-f410bf512bcf7983:hover{background:#15803d;border-color:#15803d}.btn-warning.jsx-f410bf512bcf7983{background:#f59e0b;color:#fff;border-color:#f59e0b}.btn-warning.jsx-f410bf512bcf7983:hover{background:#d97706;border-color:#d97706}.btn-danger.jsx-f410bf512bcf7983{background:#dc2626;color:#fff;border-color:#dc2626}.btn-danger.jsx-f410bf512bcf7983:hover{background:#b91c1c;border-color:#b91c1c}.btn-sm.jsx-f410bf512bcf7983{padding:4px 8px;font-size:12px}.modal-overlay.jsx-f410bf512bcf7983{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.5);display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;z-index:1000}.modal.jsx-f410bf512bcf7983{background:#fff;padding:24px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;width:90%;max-width:400px}.modal.jsx-f410bf512bcf7983 h2.jsx-f410bf512bcf7983{margin:0 0 20px 0;color:#333}.form-group.jsx-f410bf512bcf7983{margin-bottom:16px}.form-group.jsx-f410bf512bcf7983 label.jsx-f410bf512bcf7983{display:block;margin-bottom:4px;color:#333;font-weight:500}.form-group.jsx-f410bf512bcf7983 input.jsx-f410bf512bcf7983{width:100%;padding:8px 12px;border:1px solid#ddd;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;font-size:14px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.form-group.jsx-f410bf512bcf7983 input.jsx-f410bf512bcf7983:focus{outline:none;border-color:#4285f4}.form-actions.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px;-webkit-box-pack:end;-webkit-justify-content:flex-end;-moz-box-pack:end;-ms-flex-pack:end;justify-content:flex-end}@media(max-width:768px){.header-content.jsx-f410bf512bcf7983{padding:12px 16px;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px;-webkit-box-align:stretch;-webkit-align-items:stretch;-moz-box-align:stretch;-ms-flex-align:stretch;align-items:stretch}.logo.jsx-f410bf512bcf7983{-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.header-actions.jsx-f410bf512bcf7983{-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.main-content.jsx-f410bf512bcf7983{padding:16px}.stats-grid.jsx-f410bf512bcf7983{grid-template-columns:1fr;gap:16px}.section-header.jsx-f410bf512bcf7983{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px;-webkit-box-align:stretch;-webkit-align-items:stretch;-moz-box-align:stretch;-ms-flex-align:stretch;align-items:stretch;padding:16px 16px 0 16px}.search-box.jsx-f410bf512bcf7983 input.jsx-f410bf512bcf7983{width:100%}.table-header.jsx-f410bf512bcf7983,.table-row.jsx-f410bf512bcf7983{grid-template-columns:1fr;gap:8px;padding:12px 16px}.table-header.jsx-f410bf512bcf7983>div.jsx-f410bf512bcf7983,.table-row.jsx-f410bf512bcf7983>div.jsx-f410bf512bcf7983{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.table-header.jsx-f410bf512bcf7983>div.jsx-f410bf512bcf7983::before,.table-row.jsx-f410bf512bcf7983>div.jsx-f410bf512bcf7983::before{content:attr(data-label);font-weight:600;color:#6b7280;font-size:12px}.col-code.jsx-f410bf512bcf7983::before{content:\"激活码\"}.col-status.jsx-f410bf512bcf7983::before{content:\"状态\"}.col-created.jsx-f410bf512bcf7983::before{content:\"创建时间\"}.col-expires.jsx-f410bf512bcf7983::before{content:\"过期时间\"}.col-user.jsx-f410bf512bcf7983::before{content:\"使用者\"}.col-actions.jsx-f410bf512bcf7983::before{content:\"操作\"}}.footer.jsx-f410bf512bcf7983{margin-top:32px;color:#9ca3af;font-size:13px;text-align:center}.powered-by.jsx-f410bf512bcf7983{display:-webkit-inline-box;display:-webkit-inline-flex;display:-moz-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:4px}.icon.jsx-f410bf512bcf7983{-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.footer.jsx-f410bf512bcf7983 a.jsx-f410bf512bcf7983{color:#6b7280;text-decoration:none;font-weight:500;margin-left:4px}.footer.jsx-f410bf512bcf7983 a.jsx-f410bf512bcf7983:hover{color:#374151;text-decoration:underline}@media(max-width:1023px){.main-card.jsx-f410bf512bcf7983{max-width:100%;margin:0;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0;border-left:none;border-right:none}.container.jsx-f410bf512bcf7983{padding:0;-webkit-box-pack:start;-webkit-justify-content:flex-start;-moz-box-pack:start;-ms-flex-pack:start;justify-content:flex-start}}@media(max-width:768px){.header.jsx-f410bf512bcf7983{padding:16px 20px}.header-content.jsx-f410bf512bcf7983{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start}.header-actions.jsx-f410bf512bcf7983{width:100%;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}.main-content.jsx-f410bf512bcf7983{padding:20px}.logo-icon.jsx-f410bf512bcf7983{font-size:24px}.title-section.jsx-f410bf512bcf7983 h1.jsx-f410bf512bcf7983{font-size:20px}.subtitle.jsx-f410bf512bcf7983{font-size:13px}}@media(max-width:480px){.header.jsx-f410bf512bcf7983{padding:12px 16px}.main-content.jsx-f410bf512bcf7983{padding:16px}.stats-grid.jsx-f410bf512bcf7983{grid-template-columns:1fr;gap:16px}.stat-card.jsx-f410bf512bcf7983{padding:20px}.btn.jsx-f410bf512bcf7983{padding:8px 12px;font-size:13px}.header-actions.jsx-f410bf512bcf7983{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:8px}}'\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_s(AdminDashboard, \"OWLbtByhDrS+rQNDEy0+OPrd61Q=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/admin/dashboard.js\n"));

/***/ })

});